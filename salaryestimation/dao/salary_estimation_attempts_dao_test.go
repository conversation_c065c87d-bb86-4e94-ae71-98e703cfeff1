package dao

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/gamma/api/salaryestimation"
	salaryestimation2 "github.com/epifi/gamma/salaryestimation"
)

var (
	affectedTestTables = []string{"salary_estimation_attempts"}
)

func getSalaryEstimationAttemptDao(t *testing.T) (salaryestimation2.SalaryEstimationAttemptDao, func()) {
	dbInstance, dbInstanceRelease := dbInstancePool.GetDbInstance(t)
	salaryEstimationAttemptDao := NewSalaryEstimationAttemptPgDbDao(dbInstance.GetConnection())
	return salaryEstimationAttemptDao, func() { dbInstanceRelease(affectedTestTables) }
}

func TestSalaryEstimationAttemptDao_Create(t *testing.T) {
	t.<PERSON>()
	salaryEstimationAttemptDao, releaseDao := getSalaryEstimationAttemptDao(t)
	defer releaseDao()

	type args struct {
		ctx     context.Context
		attempt *salaryestimation.SalaryEstimationAttempt
	}
	tests := []struct {
		name    string
		args    args
		want    *salaryestimation.SalaryEstimationAttempt
		wantErr bool
	}{
		{
			name: "successful creation",
			args: args{
				ctx: context.Background(),
				attempt: &salaryestimation.SalaryEstimationAttempt{
					ClientReqId: "test-client-req-id-1",
					Source:      salaryestimation.Source_SOURCE_AA,
					ActorId:     "test-actor-id-1",
					Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED,
				},
			},
			want: &salaryestimation.SalaryEstimationAttempt{
				ClientReqId: "test-client-req-id-1",
				Source:      salaryestimation.Source_SOURCE_AA,
				ActorId:     "test-actor-id-1",
				Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := salaryEstimationAttemptDao.Create(tt.args.ctx, tt.args.attempt)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&salaryestimation.SalaryEstimationAttempt{}, "id", "created_at", "updated_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Create() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestSalaryEstimationAttemptDao_GetByClientReqID(t *testing.T) {
	t.Parallel()

	salaryEstimationAttemptDao, releaseDao := getSalaryEstimationAttemptDao(t)
	defer releaseDao()

	type args struct {
		ctx         context.Context
		clientReqID string
	}
	tests := []struct {
		name    string
		args    args
		want    *salaryestimation.SalaryEstimationAttempt
		wantErr bool
		setup   func() *salaryestimation.SalaryEstimationAttempt
	}{
		{
			name: "successful retrieval",
			args: args{
				ctx:         context.Background(),
				clientReqID: "unique-client-req-id",
			},
			want: &salaryestimation.SalaryEstimationAttempt{
				ClientReqId: "unique-client-req-id",
				Source:      salaryestimation.Source_SOURCE_AA,
				ActorId:     "test-actor-id",
				Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED,
			},
			wantErr: false,
			setup: func() *salaryestimation.SalaryEstimationAttempt {
				attempt := &salaryestimation.SalaryEstimationAttempt{
					ClientReqId: "unique-client-req-id",
					Source:      salaryestimation.Source_SOURCE_AA,
					ActorId:     "test-actor-id",
					Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED,
				}
				created, err := salaryEstimationAttemptDao.Create(context.Background(), attempt)
				require.NoError(t, err)
				return created
			},
		},
		{
			name: "not found",
			args: args{
				ctx:         context.Background(),
				clientReqID: "non-existent-client-req-id",
			},
			want:    nil,
			wantErr: true,
			setup:   func() *salaryestimation.SalaryEstimationAttempt { return nil },
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_ = tt.setup()
			got, err := salaryEstimationAttemptDao.GetByClientReqID(tt.args.ctx, tt.args.clientReqID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByClientReqID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&salaryestimation.SalaryEstimationAttempt{}, "id", "created_at", "updated_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetByClientReqID() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func TestSalaryEstimationAttemptDao_Update(t *testing.T) {
	t.Parallel()
	testAttemptId := uuid.NewString()
	testAttemptClientReqId := "test-client-req-id"
	testActorId := "test-actor-id"

	salaryEstimationAttemptDao, releaseDao := getSalaryEstimationAttemptDao(t)
	defer releaseDao()

	type args struct {
		ctx        context.Context
		attempt    *salaryestimation.SalaryEstimationAttempt
		fieldMasks []salaryestimation.SalaryEstimationAttemptFieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    *salaryestimation.SalaryEstimationAttempt
		wantErr bool
		setup   func() *salaryestimation.SalaryEstimationAttempt
	}{
		{
			name: "successful status update",
			args: args{
				ctx: context.Background(),
				fieldMasks: []salaryestimation.SalaryEstimationAttemptFieldMask{
					salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS,
				},
				attempt: &salaryestimation.SalaryEstimationAttempt{
					Id:     testAttemptId,
					Status: salaryestimation.AttemptStatus_ATTEMPT_STATUS_COMPLETED,
				},
			},
			want: &salaryestimation.SalaryEstimationAttempt{
				Id:          testAttemptId,
				ClientReqId: testAttemptClientReqId,
				Source:      salaryestimation.Source_SOURCE_AA,
				ActorId:     testActorId,
				Step:        salaryestimation.AttemptStep_ATTEMPT_STEP_ANALYSIS,
		Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL,
			},
			wantErr: false,
			setup: func() *salaryestimation.SalaryEstimationAttempt {
				attempt := &salaryestimation.SalaryEstimationAttempt{
					Id:          testAttemptId,
					ClientReqId: testAttemptClientReqId,
					Source:      salaryestimation.Source_SOURCE_AA,
					ActorId:     testActorId,
					Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED,
				}
				created, err := salaryEstimationAttemptDao.Create(context.Background(), attempt)
				require.NoError(t, err)
				return created
			},
		},
		{
			name: "empty id should fail",
			args: args{
				ctx: context.Background(),
				attempt: &salaryestimation.SalaryEstimationAttempt{
					Id:     "", // empty ID
					Status: salaryestimation.AttemptStatus_ATTEMPT_STATUS_COMPLETED,
				},
				fieldMasks: []salaryestimation.SalaryEstimationAttemptFieldMask{
					salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS,
				},
			},
			want:    nil,
			wantErr: true,
			setup:   func() *salaryestimation.SalaryEstimationAttempt { return nil },
		},
		{
			name: "empty field masks should fail",
			args: args{
				ctx: context.Background(),
				attempt: &salaryestimation.SalaryEstimationAttempt{
					Id:     testAttemptId,
					Status: salaryestimation.AttemptStatus_ATTEMPT_STATUS_COMPLETED,
				},
				fieldMasks: []salaryestimation.SalaryEstimationAttemptFieldMask{}, // empty field masks
			},
			want:    nil,
			wantErr: true,
			setup:   func() *salaryestimation.SalaryEstimationAttempt { return nil },
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_ = tt.setup()
			got, err := salaryEstimationAttemptDao.Update(tt.args.ctx, tt.args.attempt, tt.args.fieldMasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&salaryestimation.SalaryEstimationAttempt{}, "id", "created_at", "updated_at"),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("Update() mismatch (-want +got):\n%s", diff)
			}
			//// Verify the record was actually updated in the database
			//retrieved, err := salaryEstimationAttemptDao.GetByClientReqID(context.Background(), got.GetClientReqId())
			//require.NoError(t, err)
			//require.Equal(t, salaryestimation.AttemptStatus_ATTEMPT_STATUS_COMPLETED, retrieved.GetStatus())
		})
	}
}
