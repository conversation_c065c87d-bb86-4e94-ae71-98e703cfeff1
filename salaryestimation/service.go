package salaryestimation

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	caPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/analytics"
	caDataAnalytics "github.com/epifi/gamma/api/connected_account/data_analytics"
	"github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/datasharing"
	"github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/salaryestimation"
	datasharingTypes "github.com/epifi/gamma/api/typesv2/datasharing"
	salaryEstimationScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryestimation"
	salaryEstimationTypes "github.com/epifi/gamma/api/typesv2/salaryestimation"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	salaryestimationPkg "github.com/epifi/gamma/pkg/salaryestimation"
	genConf "github.com/epifi/gamma/salaryestimation/config/genconf"
)

type Service struct {
	conf                       *genConf.Config
	caAnalyticsClient          analytics.AnalyticsClient
	caDataAnalyticsClient      caDataAnalytics.DataAnalyticsClient
	consentClient              consent.ConsentClient
	dataSharingClient          datasharing.DataSharingClient
	connectedAccountClient     caPb.ConnectedAccountClient
	preApprovedLoanClient      palPb.PreApprovedLoanClient
	eventBroker                events.Broker
	salaryEstimationAttemptDao SalaryEstimationAttemptDao
}

func NewService(
	conf *genConf.Config,
	caAnalyticsClient analytics.AnalyticsClient,
	caDataAnalyticsClient caDataAnalytics.DataAnalyticsClient,
	consentClient consent.ConsentClient,
	dataSharingClient datasharing.DataSharingClient,
	connectedAccountClient caPb.ConnectedAccountClient,
	preApprovedLoanClient palPb.PreApprovedLoanClient,
	eventBroker events.Broker,
	salaryEstimationAttemptDao SalaryEstimationAttemptDao,
) *Service {
	return &Service{
		conf:                       conf,
		caAnalyticsClient:          caAnalyticsClient,
		caDataAnalyticsClient:      caDataAnalyticsClient,
		consentClient:              consentClient,
		dataSharingClient:          dataSharingClient,
		connectedAccountClient:     connectedAccountClient,
		preApprovedLoanClient:      preApprovedLoanClient,
		eventBroker:                eventBroker,
		salaryEstimationAttemptDao: salaryEstimationAttemptDao,
	}
}

func (s *Service) GetSalary(ctx context.Context, req *salaryestimation.GetSalaryRequest) (*salaryestimation.GetSalaryResponse, error) {
	res, err := s.caDataAnalyticsClient.GetAnalysisByActor(ctx, &caDataAnalytics.GetAnalysisByActorRequest{ActorId: req.GetActorId()})
	if err = epifigrpc.RPCError(res, err); err != nil {
		logger.Error(ctx, "error getting analysis by actor", zap.Error(err))
		if res.GetStatus().IsRecordNotFound() {
			return &salaryestimation.GetSalaryResponse{Status: rpc.StatusRecordNotFound()}, nil
		}
		return &salaryestimation.GetSalaryResponse{Status: rpc.StatusInternal()}, nil
	}
	var salaryAccountId string
	for accountId, accountAnalysis := range res.GetAnalysis().GetAccountAnalyses() {
		if accountAnalysis.GetStatistics().GetIsSalaryAccount().ToBool() {
			salaryAccountId = accountId
			break
		}
	}
	return &salaryestimation.GetSalaryResponse{
		Status: rpc.StatusOk(),
		Salary: &salaryestimation.Salary{
			Source:        salaryEstimationTypes.Source_SOURCE_AA,
			SalaryAccount: &salaryestimation.SalaryAccount{AccountId: salaryAccountId},
			ComputedAt:    res.GetAnalysis().GetL2AnalysisCompletedAt(),
		},
		L1AnalysisSignedUrl: res.GetL1AnalysisSignedUrl(),
	}, nil
}

func (s *Service) ComputeSalary(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*salaryestimation.ComputeSalaryResponse, error) {
	// Check if stateful salary estimation is enabled
	if s.conf.Flags().EnableStatefulSalaryEstimation() {
		logger.Info(ctx, "stateful salary estimation enabled, checking for existing attempt", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return s.computeSalaryWithStatefulLogic(ctx, req)
	}

	// Fallback to original logic if flag is disabled
	switch req.GetSource() {
	case salaryEstimationTypes.Source_SOURCE_AA:
		nextAction, err := s.computeSalaryViaAa(ctx, req)
		if err != nil {
			logger.Error(ctx, "error computing salary for AA flow", zap.Error(err))
			return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
		}
		logger.Info(ctx, "next action data of computed salary for AA flow", zap.String(logger.SCREEN, nextAction.GetScreen().String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Any("aa_data_flow_params", req.GetSourceFlowParams().GetAaDataFlowParams()))
		return &salaryestimation.ComputeSalaryResponse{
			Status:     rpc.StatusOk(),
			NextAction: nextAction,
		}, nil
	default:
		logger.Error(ctx, "no source specified, screen for choosing source is not yet supported")
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInvalidArgument()}, nil
	}
}

func (s *Service) computeSalaryWithStatefulLogic(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*salaryestimation.ComputeSalaryResponse, error) {
	existingAttempt, err := s.salaryEstimationAttemptDao.GetByClientReqID(ctx, req.GetClientReqId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error getting existing attempt", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
	}
	if existingAttempt != nil {
		// Existing attempt found, handle based on status
		logger.Info(ctx, "existing attempt found", zap.String("attempt_id", existingAttempt.GetId()), zap.String("status", existingAttempt.GetStatus().String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return s.handleExistingAttempt(ctx, req, existingAttempt)
	}
	logger.Info(ctx, "no existing attempt found, creating new attempt", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	nextAction, err := s.createNewAttemptAndProceed(ctx, req)
	if err != nil {
		logger.Error(ctx, "error creating new attempt and proceeding", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
	}
	return &salaryestimation.ComputeSalaryResponse{
		Status:     rpc.StatusOk(),
		NextAction: nextAction,
	}, nil
}

func (s *Service) computeSalaryViaAa(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	if req.GetRequireHoldingScreen() {
		return getHoldingScreenForAAAnalysis(req.GetClient(), req.GetClientReqId()), nil
	}
	err := s.initiateWealthAnalysis(ctx, req.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error initiating analysis")
	}
	logger.Info(ctx, "checking analysis status from data analytics", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	analysisStatusRes, err := s.caDataAnalyticsClient.GetAnalysisStatus(ctx, &caDataAnalytics.GetAnalysisStatusRequest{
		ActorId:     req.GetActorId(),
		ClientReqId: req.GetClientReqId(),
	})
	if err = epifigrpc.RPCError(analysisStatusRes, err); err != nil {
		if analysisStatusRes.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "analysis not found, getting next action for customer", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
			nextAction, computeErr := s.computeSalaryForFlowParams(ctx, req)
			if computeErr != nil {
				return nil, errors.Wrap(computeErr, "error computing salary for flow params")
			}
			logger.Info(ctx, "next action for customer", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.String(logger.SCREEN, nextAction.GetScreen().String()))
			return nextAction, nil
		}
		logger.Info(ctx, "error getting analysis status", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return nil, errors.Wrap(err, "error getting analysis status")
	}
	logger.Info(ctx, fmt.Sprintf("got analysis status: %v from data analytics", analysisStatusRes.GetAnalysisStatus().String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	analysisStatus := getSalaryEstAnalysisStatusFromCaAnalysisStatus(analysisStatusRes.GetAnalysisStatus())
	logger.Info(ctx, fmt.Sprintf("salary estimation analysis status: %v", analysisStatus.String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	nextAction, nextActionErr := s.getLatestAnalysisStatusScreen(ctx, req, req.GetClientReqId(), analysisStatus)
	if nextActionErr != nil {
		return nil, fmt.Errorf("error getting latest analysis status screen: %w", nextActionErr)
	}
	logger.Info(ctx, "next action for customer", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.String(logger.SCREEN, nextAction.GetScreen().String()))
	return nextAction, nil
}

func (s *Service) computeSalaryForFlowParams(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	aaDataFlowParams := req.GetSourceFlowParams().GetAaDataFlowParams()
	logger.Info(ctx, fmt.Sprintf("getting next action for aa data flow params: %v", aaDataFlowParams.GetInputType().String()), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
	switch aaDataFlowParams.GetInputType() {
	default:
		logger.Info(ctx, "no input type specified, assuming fresh visit to flow")
		return s.getNextActionForFreshVisit(ctx, req)
	case salaryEstimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_DATA_SHARE:
		return s.getNextActionForDataShareInput(ctx, req)
	case salaryEstimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_DATA_STATUS:
		return s.getNextActionForDataStatusInput(ctx, req)
	case salaryEstimationTypes.AaDataFlowInputType_AA_DATA_FLOW_INPUT_TYPE_ANALYSIS:
		return s.getNextActionForAnalysisInput(ctx, req)
	}
}

func (s *Service) initiateWealthAnalysis(ctx context.Context, actorId string) error {
	crId := uuid.NewString()
	analysisInitRes, err := s.caAnalyticsClient.InitiateAnalysis(ctx, &analytics.InitiateAnalysisRequest{
		ActorId:     actorId,
		ClientReqId: crId,
		Client:      common.Owner_OWNER_EPIFI_WEALTH,
	})
	if err = epifigrpc.RPCError(analysisInitRes, err); err != nil {
		if analysisInitRes.GetStatus().IsAlreadyExists() ||
			analysisInitRes.GetStatus().GetCode() == uint32(analytics.InitiateAnalysisResponse_ANOTHER_ANALYSIS_FOR_ACTOR_ALREADY_IN_PROGRESS) ||
			analysisInitRes.GetStatus().GetCode() == uint32(analytics.InitiateAnalysisResponse_NO_NEW_DATA_FOR_ANALYSIS_FOR_ANY_ACCOUNT_FOR_ACTOR) ||
			analysisInitRes.GetStatus().GetCode() == uint32(analytics.InitiateAnalysisResponse_AA_DATA_PULL_IN_PROGRESS_FOR_ACTOR) {
			logger.Info(ctx, fmt.Sprintf("fire and forget initiate analysis custom handling, status code: %d, msg: %s",
				analysisInitRes.GetStatus().GetCode(), analysisInitRes.GetStatus().GetShortMessage()), zap.String(logger.CLIENT_REQUEST_ID, crId))
			return nil
		}
		return errors.Wrapf(err, "error initiating analysis for actor_id: %s, client_req_id: %s", actorId, crId)
	}
	logger.Info(ctx, "New analysis initiated with new client req id", zap.String(logger.CLIENT_REQUEST_ID, analysisInitRes.GetClientReqId()))
	return nil
}

func (s *Service) getNextActionForDataShareInput(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	dataSharingConsentId, err := s.getConsentIdForDataSharing(ctx, req.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting consent id for data sharing")
	}
	dataShareParams := req.GetSourceFlowParams().GetAaDataFlowParams().GetDataShareParams()
	initDataShareRes, err := s.dataSharingClient.InitiateDataSharing(ctx, &datasharing.InitiateDataSharingRequest{
		Client:      datasharingTypes.Client_CLIENT_SALARY_ESTIMATION,
		ClientReqId: req.GetClientReqId(),
		ActorId:     req.GetActorId(),
		DataType:    datasharingTypes.DataType_DATA_TYPE_AA_ACCOUNTS,
		DataRequestParams: &datasharingTypes.DataRequestParams{
			ReqParams: &datasharingTypes.DataRequestParams_AaAccountsDataQueryParams{
				AaAccountsDataQueryParams: &datasharingTypes.AaAccountsDataQueryParams{
					AccountIds:          dataShareParams.GetAaAccountIds(),
					OldestTransactionTs: timestamppb.New(s.conf.OldestAATransactionUpdatedAt()),
					LatestTransactionTs: timestamppb.Now(),
					ConsentId:           dataSharingConsentId,
				},
			},
		},
	})
	if err = epifigrpc.RPCError(initDataShareRes, err); err != nil {
		return nil, errors.Wrap(err, "error initiating data sharing")
	}
	return initDataShareRes.GetNextActionDeeplink(), nil
}

func (s *Service) getConsentIdForDataSharing(ctx context.Context, actorId string) (string, error) {
	perpetualConsentRes, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ActorId:     actorId,
		ConsentType: consent.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2,
		Owner:       common.Owner_OWNER_EPIFI_WEALTH,
	})
	if err = epifigrpc.RPCError(perpetualConsentRes, err); err != nil && !perpetualConsentRes.GetStatus().IsRecordNotFound() {
		return "", fmt.Errorf("error fetching perpetual consent: %w", err)
	}
	if perpetualConsentRes.GetConsentId() != "" {
		return perpetualConsentRes.GetConsentId(), nil
	}
	latestOneTimeDataSharingConsent, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ConsentType: consent.ConsentType_CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP,
		ActorId:     actorId,
		Owner:       common.Owner_OWNER_EPIFI_TECH,
	})
	if err = epifigrpc.RPCError(latestOneTimeDataSharingConsent, err); err != nil && !latestOneTimeDataSharingConsent.GetStatus().IsRecordNotFound() {
		return "", errors.Wrap(err, "error fetching latest one time data sharing consent")
	}
	if time.Now().After(latestOneTimeDataSharingConsent.GetExpiresAt().AsTime()) {
		return "", errors.Errorf("latest one time data sharing consent expired at: %s", latestOneTimeDataSharingConsent.GetExpiresAt().AsTime())
	}
	return latestOneTimeDataSharingConsent.GetConsentId(), nil
}

func (s *Service) getNextActionForDataStatusInput(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	dataSharingRecord := req.GetSourceFlowParams().GetAaDataFlowParams().GetDataStatusParams().GetDataSharingRecord()
	analysisAttemptClientReqId := dataSharingRecord.GetClientRequestId()
	var files []*analytics.File
	for _, sharedFile := range dataSharingRecord.GetData().GetAaAccountsData().GetFiles() {
		file := &analytics.File{
			FilePath: sharedFile.GetUrl(),
			FileMetadata: &analytics.FileMetadata{
				AccountsMetadata: []*analytics.AccountMetadata{
					{
						AccountId:           sharedFile.GetMetadata().GetAaAccountFileMetadata().GetAccountId(),
						OldestTransactionTs: sharedFile.GetMetadata().GetAaAccountFileMetadata().GetOldestTransactionTs(),
						LatestTransactionTs: sharedFile.GetMetadata().GetAaAccountFileMetadata().GetLatestTransactionTs(),
					},
				},
			},
		}
		files = append(files, file)
	}
	res, err := s.caDataAnalyticsClient.InitiateAnalysis(ctx, &caDataAnalytics.InitiateAnalysisRequest{
		ClientReqId: analysisAttemptClientReqId,
		ActorId:     dataSharingRecord.GetActorId(),
		RequestParams: &caDataAnalytics.AnalysisRequestParams{
			DataExchangeRecord: &analytics.DataExchangeRecord{
				ClientReqId: dataSharingRecord.GetClientRequestId(),
				ActorId:     dataSharingRecord.GetActorId(),
				Owner:       dataSharingRecord.GetClientOwnership(),
				Data: &analytics.Data{
					Files: files,
				},
			},
		},
	})
	if err = epifigrpc.RPCError(res, err); err != nil && !res.GetStatus().IsAlreadyExists() {
		return nil, fmt.Errorf("error initiating analysis: %w", err)
	}
	analysisStatus := getSalaryEstAnalysisStatusFromCaAnalysisStatus(res.GetAnalysisStatus())
	return s.getLatestAnalysisStatusScreen(ctx, req, analysisAttemptClientReqId, analysisStatus)
}

func getSalaryEstAnalysisStatusFromCaAnalysisStatus(caAnalysisStatus caDataAnalytics.AnalysisStatus) salaryEstimationTypes.AnalysisStatus {
	switch caAnalysisStatus {
	default:
		return salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_UNSPECIFIED
	case caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_CREATED,
		caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_INITIATED,
		caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_ANALYSIS_PENDING:
		return salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_IN_PROGRESS
	case caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL:
		return salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL
	case caDataAnalytics.AnalysisStatus_ANALYSIS_STATUS_FAILED:
		return salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED
	}
}

func getHoldingScreenForAAAnalysis(client salaryestimation.Client, clientReqId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_VERIFY_INCOME_HOME_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.VerifyIncomeHomeScreenOptions{
			Client:      client.String(),
			ClientReqId: clientReqId,
			Source:      salaryEstimationTypes.Source_SOURCE_AA.String(),
		}),
	}
}

func (s *Service) handleExistingAttempt(ctx context.Context, req *salaryestimation.ComputeSalaryRequest, attempt *salaryestimation.SalaryEstimationAttempt) (*salaryestimation.ComputeSalaryResponse, error) {
	switch attempt.GetStatus() {
	case salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED:
		// Attempt is initiated, proceed with analysis initiation
		logger.Info(ctx, "attempt is initiated, proceeding with analysis", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return s.proceedWithAnalysisInitiation(ctx, req, attempt)
	case salaryestimation.AttemptStatus_ATTEMPT_STATUS_DATA_SHARING_IN_PROGRESS,
		salaryestimation.AttemptStatus_ATTEMPT_STATUS_ANALYSIS_IN_PROGRESS:
		// Analysis is in progress, check current status and return appropriate screen
		logger.Info(ctx, "attempt is in progress, checking current analysis status", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return s.handleInProgressAttempt(ctx, req, attempt)

	case salaryestimation.AttemptStatus_ATTEMPT_STATUS_COMPLETED,
		salaryestimation.AttemptStatus_ATTEMPT_STATUS_ANALYSIS_COMPLETED:
		// Analysis completed successfully
		logger.Info(ctx, "attempt completed successfully", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return s.handleCompletedAttempt(ctx, req, attempt)

	case salaryestimation.AttemptStatus_ATTEMPT_STATUS_FAILED,
		salaryestimation.AttemptStatus_ATTEMPT_STATUS_DATA_SHARING_FAILED,
		salaryestimation.AttemptStatus_ATTEMPT_STATUS_ANALYSIS_FAILED:
		// Analysis failed, handle failure case
		logger.Info(ctx, "attempt failed, handling failure case", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return s.handleFailedAttempt(ctx, req, attempt)
	default:
		return nil, errors.Errorf("unhandled existing attempt status: %s", attempt.GetStatus())
	}
}

func (s *Service) createNewAttemptAndProceed(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	source, err := s.convertSourceToAttemptSource(req.GetSource())
	if err != nil {
		return nil, errors.Wrap(err, "error converting source to attempt source")
	}
	newAttempt := &salaryestimation.SalaryEstimationAttempt{
		ClientReqId: req.GetClientReqId(),
		Source:      source,
		ActorId:     req.GetActorId(),
		Step:        salaryestimation.AttemptStep_ATTEMPT_STEP_SALARY_ESTIMATION,
		Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED,
	}
	createdAttempt, err := s.salaryEstimationAttemptDao.Create(ctx, newAttempt)
	if err != nil {
		return nil, errors.Wrap(err, "error creating new attempt")
	}
	err = s.initiateWealthAnalysis(ctx, req.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error initiating wealth analysis")
	}
	nextAction, err := s.processConsentAndDataSharingFlow(ctx, req, createdAttempt.GetId())
	if err != nil {
		return nil, errors.Wrap(err, "error processing consent and data sharing flow for new attempt")
	}
	return nextAction, nil
}

func (s *Service) proceedWithAnalysisInitiation(ctx context.Context, req *salaryestimation.ComputeSalaryRequest, attempt *salaryestimation.SalaryEstimationAttempt) (*salaryestimation.ComputeSalaryResponse, error) {
	// Initiate analysis if not already done
	err := s.initiateWealthAnalysis(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error initiating analysis", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		// Update attempt status to failed
		s.updateAttemptStatus(ctx, attempt.GetId(), salaryestimation.AttemptStatus_ATTEMPT_STATUS_FAILED)
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
	}

	// Implement the consent and data sharing flow for existing attempt
	nextAction, err := s.processConsentAndDataSharingFlow(ctx, req, attempt.GetId())
	if err != nil {
		logger.Error(ctx, "error processing consent and data sharing flow for existing attempt", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		// Update attempt status to failed
		s.updateAttemptStatus(ctx, attempt.GetId(), salaryestimation.AttemptStatus_ATTEMPT_STATUS_FAILED)
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
	}

	return &salaryestimation.ComputeSalaryResponse{
		Status:     rpc.StatusOk(),
		NextAction: nextAction,
	}, nil
}

func (s *Service) handleInProgressAttempt(ctx context.Context, req *salaryestimation.ComputeSalaryRequest, attempt *salaryestimation.SalaryEstimationAttempt) (*salaryestimation.ComputeSalaryResponse, error) {
	// Use existing computeSalaryViaAa logic to handle in-progress attempts
	nextAction, err := s.computeSalaryViaAa(ctx, req)
	if err != nil {
		logger.Error(ctx, "error computing salary for in-progress attempt", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
	}

	return &salaryestimation.ComputeSalaryResponse{
		Status:     rpc.StatusOk(),
		NextAction: nextAction,
	}, nil
}

func (s *Service) handleCompletedAttempt(ctx context.Context, req *salaryestimation.ComputeSalaryRequest, attempt *salaryestimation.SalaryEstimationAttempt) (*salaryestimation.ComputeSalaryResponse, error) {
	// For completed attempts, use existing logic to get the appropriate next action
	nextAction, err := s.computeSalaryViaAa(ctx, req)
	if err != nil {
		logger.Error(ctx, "error computing salary for completed attempt", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
	}

	return &salaryestimation.ComputeSalaryResponse{
		Status:     rpc.StatusOk(),
		NextAction: nextAction,
	}, nil
}

func (s *Service) handleFailedAttempt(ctx context.Context, req *salaryestimation.ComputeSalaryRequest, attempt *salaryestimation.SalaryEstimationAttempt) (*salaryestimation.ComputeSalaryResponse, error) {
	// For failed attempts, use existing logic to handle failure scenarios
	nextAction, err := s.computeSalaryViaAa(ctx, req)
	if err != nil {
		logger.Error(ctx, "error computing salary for failed attempt", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return &salaryestimation.ComputeSalaryResponse{Status: rpc.StatusInternal()}, nil
	}

	return &salaryestimation.ComputeSalaryResponse{
		Status:     rpc.StatusOk(),
		NextAction: nextAction,
	}, nil
}

func (s *Service) updateAttemptStatus(ctx context.Context, attemptId string, status salaryestimation.AttemptStatus) {
	attempt := &salaryestimation.SalaryEstimationAttempt{
		Id:        attemptId,
		Status:    status,
		UpdatedAt: timestamppb.Now(),
	}

	_, err := s.salaryEstimationAttemptDao.Update(ctx, attempt, []salaryestimation.SalaryEstimationAttemptFieldMask{
		salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS,
	})
	if err != nil {
		logger.Error(ctx, "error updating attempt status", zap.Error(err), zap.String("attempt_id", attemptId), zap.String("status", status.String()))
	} else {
		logger.Info(ctx, "updated attempt status", zap.String("attempt_id", attemptId), zap.String("status", status.String()))
	}
}

func (s *Service) processConsentAndDataSharingFlow(ctx context.Context, req *salaryestimation.ComputeSalaryRequest, attemptId string) (*deeplink.Deeplink, error) {
	perpetualConsentRes, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ActorId:     req.GetActorId(),
		ConsentType: consent.ConsentType_CONSENT_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP_V2,
		Owner:       common.Owner_OWNER_EPIFI_WEALTH,
	})
	if err = epifigrpc.RPCError(perpetualConsentRes, err); err != nil && !perpetualConsentRes.GetStatus().IsRecordNotFound() {
		return nil, errors.Wrap(err, "error fetching perpetual consent")
	}
	if perpetualConsentRes.GetConsentId() != "" {
		return s.handlePerpetualConsentFlow(ctx, req, attemptId, perpetualConsentRes.GetConsentId())
	}
	oneTimeConsentRes, err := s.consentClient.FetchConsent(ctx, &consent.FetchConsentRequest{
		ConsentType: consent.ConsentType_CONSENT_TYPE_ONE_TIME_EPIFI_WEALTH_DATA_SHARING_WITH_EPIFI_GROUP,
		ActorId:     req.GetActorId(),
		Owner:       common.Owner_OWNER_EPIFI_TECH,
	})
	if err = epifigrpc.RPCError(oneTimeConsentRes, err); err != nil && !oneTimeConsentRes.GetStatus().IsRecordNotFound() {
		return nil, errors.Wrap(err, "error fetching one-time consent")
	}
	hasValidOneTimeConsent := false
	if oneTimeConsentRes.GetConsentId() != "" && oneTimeConsentRes.GetExpiresAt() != nil {
		if time.Now().Before(oneTimeConsentRes.GetExpiresAt().AsTime()) {
			hasValidOneTimeConsent = true
			logger.Info(ctx, "valid one-time consent found", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		} else {
			logger.Info(ctx, "one-time consent found but expired", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		}
	}
	return s.handleAccountConnectionFlow(ctx, req, attemptId, hasValidOneTimeConsent)
}

func (s *Service) handlePerpetualConsentFlow(ctx context.Context, req *salaryestimation.ComputeSalaryRequest, attemptId string, consentId string) (*deeplink.Deeplink, error) {
	// TODO(Brijesh): Store consent id
	s.updateAttemptStatus(ctx, attemptId, salaryestimation.AttemptStatus_ATTEMPT_STATUS_DATA_SHARING_IN_PROGRESS)
	accounts, err := salaryestimationPkg.GetAccountsToBeAnalysedForActor(ctx, s.connectedAccountClient, req.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			// TODO(Brijesh): Change to step = ACCOUNT_CONNECTION, status = PENDING
			return s.getVerifyIncomeHomeScreenForPerpetualConsent(ctx, req)
		}
		return nil, errors.Wrap(err, "error getting accounts to be analyzed")
	}
	// TODO(Brijesh): Change to step = DATA_SHARING, status = IN_PROGRESS
	var accountIds []string
	for _, account := range accounts {
		accountIds = append(accountIds, account.GetAccountId())
	}
	downloadDataRes, err := s.dataSharingClient.DownloadData(ctx, &datasharing.DownloadDataRequest{
		Client:          datasharingTypes.Client_CLIENT_SALARY_ESTIMATION,
		ClientOwnership: common.Owner_OWNER_EPIFI_TECH,
		ClientReqId:     req.GetClientReqId(),
		ActorId:         req.GetActorId(),
		DataType:        datasharingTypes.DataType_DATA_TYPE_AA_ACCOUNTS,
		DataRequestParams: &datasharingTypes.DataRequestParams{
			ReqParams: &datasharingTypes.DataRequestParams_AaAccountsDataQueryParams{
				AaAccountsDataQueryParams: &datasharingTypes.AaAccountsDataQueryParams{
					AccountIds:          accountIds,
					OldestTransactionTs: timestamppb.New(s.getFiDataRangeFromForSalaryEstimation()),
					LatestTransactionTs: timestamppb.Now(),
					ConsentId:           consentId,
				},
			},
		},
	})
	if err = epifigrpc.RPCError(downloadDataRes, err); err != nil {
		return nil, errors.Wrap(err, "error downloading data")
	}
	uploadDataRes, err := s.dataSharingClient.UploadData(ctx, &datasharing.UploadDataRequest{
		DataSharingRecord: downloadDataRes.GetDataSharingRecord(),
	})
	if err = epifigrpc.RPCError(uploadDataRes, err); err != nil {
		return nil, errors.Wrap(err, "error uploading data")
	}
	// TODO(Brijesh): Change to step = ANALYSIS, status = PENDING
	s.updateAttemptStatus(ctx, attemptId, salaryestimation.AttemptStatus_ATTEMPT_STATUS_ANALYSIS_IN_PROGRESS)
	return s.handleAnalysisInitiationAndStatus(ctx, req, attemptId, downloadDataRes.GetDataSharingRecord())
}

func (s *Service) handleAccountConnectionFlow(ctx context.Context, req *salaryestimation.ComputeSalaryRequest, attemptId string, hasValidOneTimeConsent bool) (*deeplink.Deeplink, error) {
	aaDataPullStatus, err := salaryestimationPkg.GetAADataPullStatus(ctx, s.connectedAccountClient, req.GetActorId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			// TODO(Brijesh): Update step = ACCOUNT_CONNECTION, status = PENDING
			return s.getVerifyIncomeHomeScreenWithConsent(ctx, req, hasValidOneTimeConsent)
		}
		return nil, errors.Wrap(err, "error getting AA data pull status")
	}
	// TODO(Brijesh): Update step = CONSENT_COLLECTION, status = PENDING
	if aaDataPullStatus.IsDataPullSuccess() {
		// Data pulls of one or more accounts are successful, return getSalaryAccountSelectionScreen
		logger.Info(ctx, "data pull successful for connected accounts", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		accountBlocks, gaErr := getAccountBlocks(aaDataPullStatus.GetSuccessAccount())
		if gaErr != nil {
			return nil, errors.Wrap(gaErr, "error getting account blocks")
		}
		return s.getSalaryAccountSelectionScreenWithConsent(ctx, req, accountBlocks, hasValidOneTimeConsent)
	} else if aaDataPullStatus.IsDataPullInProgress() {
		// TODO(Brijesh): Show appropriate copies if data pull is in progress
		// List down and then discuss all scenarios with Vikas
		// Data pull is in progress
		logger.Info(ctx, "data pull in progress for connected accounts", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return s.getLatestAnalysisStatusScreenForDataPullInProgress(ctx, req)
	} else {
		// Data pull failed, return error for now
		logger.Info(ctx, "data pull failed for connected accounts", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		s.updateAttemptStatus(ctx, attemptId, salaryestimation.AttemptStatus_ATTEMPT_STATUS_DATA_SHARING_FAILED)
		return s.getLatestAnalysisStatusScreen(ctx, req, "", salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED)
	}
}

func (s *Service) handleAnalysisInitiationAndStatus(ctx context.Context, req *salaryestimation.ComputeSalaryRequest, attemptId string, dataSharingRecord *datasharingTypes.DataSharingRecord) (*deeplink.Deeplink, error) {
	// Prepare files for analysis
	var files []*analytics.File
	for _, sharedFile := range dataSharingRecord.GetData().GetAaAccountsData().GetFiles() {
		file := &analytics.File{
			FilePath: sharedFile.GetUrl(),
			FileMetadata: &analytics.FileMetadata{
				AccountsMetadata: []*analytics.AccountMetadata{
					{
						AccountId:           sharedFile.GetMetadata().GetAaAccountFileMetadata().GetAccountId(),
						OldestTransactionTs: sharedFile.GetMetadata().GetAaAccountFileMetadata().GetOldestTransactionTs(),
						LatestTransactionTs: sharedFile.GetMetadata().GetAaAccountFileMetadata().GetLatestTransactionTs(),
					},
				},
			},
		}
		files = append(files, file)
	}

	// Initiate analysis
	analysisRes, err := s.caDataAnalyticsClient.InitiateAnalysis(ctx, &caDataAnalytics.InitiateAnalysisRequest{
		ClientReqId: req.GetClientReqId(),
		ActorId:     req.GetActorId(),
		RequestParams: &caDataAnalytics.AnalysisRequestParams{
			DataExchangeRecord: &analytics.DataExchangeRecord{
				ClientReqId: dataSharingRecord.GetClientRequestId(),
				ActorId:     dataSharingRecord.GetActorId(),
				Owner:       dataSharingRecord.GetClientOwnership(),
				Data: &analytics.Data{
					Files: files,
				},
			},
		},
	})
	if err = epifigrpc.RPCError(analysisRes, err); err != nil && !analysisRes.GetStatus().IsAlreadyExists() {
		return nil, errors.Wrap(err, "error initiating analysis")
	}
	// TODO(Brijesh): Change to step = DATA_ANALYSIS, status = IN_PROGRESS / COMPLETED / FAILED, depending upon analysisRes.GetAnalysisStatus()

	// Handle analysis status and update attempt status accordingly
	analysisStatus := getSalaryEstAnalysisStatusFromCaAnalysisStatus(analysisRes.GetAnalysisStatus())
	switch analysisStatus {
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL:
		s.updateAttemptStatus(ctx, attemptId, salaryestimation.AttemptStatus_ATTEMPT_STATUS_COMPLETED)
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED:
		s.updateAttemptStatus(ctx, attemptId, salaryestimation.AttemptStatus_ATTEMPT_STATUS_ANALYSIS_FAILED)
	case salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_IN_PROGRESS:
		// Status already set to ANALYSIS_IN_PROGRESS above
	}

	// Return the deeplink based on analysis status
	return s.getLatestAnalysisStatusScreen(ctx, req, req.GetClientReqId(), analysisStatus)
}

func (s *Service) getFiDataRangeFromForSalaryEstimation() time.Time {
	// Return 6 months from now for salary estimation flow
	return time.Now().AddDate(0, -6, 0)
}

func (s *Service) getVerifyIncomeHomeScreenForPerpetualConsent(ctx context.Context, req *salaryestimation.ComputeSalaryRequest) (*deeplink.Deeplink, error) {
	// For perpetual consent flow with no connected accounts
	consentCheckboxes := []*widget.CheckboxItem{getCheckboxForEpifiWealthAndFinvuTnc()}
	dataPullFailureDeeplink, err := s.getLatestAnalysisStatusScreen(ctx, req, "", salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED)
	if err != nil {
		return nil, errors.Wrap(err, "error getting data pull failure deeplink")
	}
	caConnectionFlowRes, err := s.connectedAccountClient.StartConnectionFlow(ctx, &caPb.StartConnectionFlowRequest{
		ClientReqId: req.GetClientReqId(),
		ActorId:     req.GetActorId(),
		CaFlowName:  enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION,
		CaFlowParams: &caPb.CAFlowParams{
			DataPullSuccessRedirectionDeeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_SALARY_ACCOUNT_SELECTION_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions{
					Client:      req.GetClient().String(),
					ClientReqId: req.GetClientReqId(),
					Source:      req.GetSource().String(),
				}),
			},
			DataPullFailureRedirectionDeeplink: dataPullFailureDeeplink,
		},
	})
	if err = epifigrpc.RPCError(caConnectionFlowRes, err); err != nil {
		return nil, errors.Wrap(err, "error starting connection flow")
	}
	return s.getVerifyIncomeHomeScreen(ctx, req, consentCheckboxes, caConnectionFlowRes.GetConnectionFlow().GetId()), nil
}

func (s *Service) getVerifyIncomeHomeScreenWithConsent(ctx context.Context, req *salaryestimation.ComputeSalaryRequest, hasValidOneTimeConsent bool) (*deeplink.Deeplink, error) {
	var consentCheckboxes []*widget.CheckboxItem
	if hasValidOneTimeConsent {
		// Add checkbox to take the one time data-sharing consent
		consentCheckboxes = append(consentCheckboxes, getCheckboxForOneTimeDataSharingConsent())
	}
	consentCheckboxes = append(consentCheckboxes, getCheckboxForEpifiWealthAndFinvuTnc())

	dataPullFailureDeeplink, err := s.getLatestAnalysisStatusScreen(ctx, req, "", salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED)
	if err != nil {
		return nil, errors.Wrap(err, "error getting data pull failure deeplink")
	}
	caConnectionFlowRes, err := s.connectedAccountClient.StartConnectionFlow(ctx, &caPb.StartConnectionFlowRequest{
		ClientReqId: req.GetClientReqId(),
		ActorId:     req.GetActorId(),
		CaFlowName:  enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION,
		CaFlowParams: &caPb.CAFlowParams{
			DataPullSuccessRedirectionDeeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_SALARY_ACCOUNT_SELECTION_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions{
					Client:      req.GetClient().String(),
					ClientReqId: req.GetClientReqId(),
					Source:      req.GetSource().String(),
				}),
			},
			DataPullFailureRedirectionDeeplink: dataPullFailureDeeplink,
		},
	})
	if err = epifigrpc.RPCError(caConnectionFlowRes, err); err != nil {
		return nil, errors.Wrap(err, "error starting connection flow")
	}
	return s.getVerifyIncomeHomeScreen(ctx, req, consentCheckboxes, caConnectionFlowRes.GetConnectionFlow().GetId()), nil
}

func (s *Service) getSalaryAccountSelectionScreenWithConsent(ctx context.Context, req *salaryestimation.ComputeSalaryRequest, accountBlocks []*salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions_BankAccountBlock, hasValidOneTimeConsent bool) (*deeplink.Deeplink, error) {
	var consentCheckboxes []*widget.CheckboxItem
	if hasValidOneTimeConsent {
		// Add checkbox to take the one time data-sharing consent
		consentCheckboxes = append(consentCheckboxes, getCheckboxForOneTimeDataSharingConsent())
	}
	consentCheckboxes = append(consentCheckboxes, getCheckboxForEpifiWealthAndFinvuTnc())

	dataPullFailureDeeplink, err := s.getLatestAnalysisStatusScreen(ctx, req, "", salaryEstimationTypes.AnalysisStatus_ANALYSIS_STATUS_FAILED)
	if err != nil {
		return nil, errors.Wrap(err, "error getting data pull failure deeplink")
	}
	caConnectionFlowRes, err := s.connectedAccountClient.StartConnectionFlow(ctx, &caPb.StartConnectionFlowRequest{
		ClientReqId: req.GetClientReqId(),
		ActorId:     req.GetActorId(),
		CaFlowName:  enums.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION,
		CaFlowParams: &caPb.CAFlowParams{
			DataPullSuccessRedirectionDeeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_SALARY_ACCOUNT_SELECTION_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryEstimationScreenOptions.SalaryAccountSelectionScreenOptions{
					Client:      req.GetClient().String(),
					ClientReqId: req.GetClientReqId(),
					Source:      req.GetSource().String(),
				}),
			},
			DataPullFailureRedirectionDeeplink: dataPullFailureDeeplink,
		},
	})
	if err = epifigrpc.RPCError(caConnectionFlowRes, err); err != nil {
		return nil, errors.Wrap(err, "error starting connection flow")
	}
	return s.getSalaryAccountSelectionScreen(ctx, req, accountBlocks, consentCheckboxes, caConnectionFlowRes.GetConnectionFlow().GetId()), nil
}

func (s *Service) convertSourceToAttemptSource(source salaryEstimationTypes.Source) (salaryestimation.Source, error) {
	switch source {
	case salaryEstimationTypes.Source_SOURCE_AA:
		return salaryestimation.Source_SOURCE_AA, nil
	default:
		return salaryestimation.Source_SOURCE_UNSPECIFIED, errors.Errorf("unhandled source: %s", source.String())
	}
}
