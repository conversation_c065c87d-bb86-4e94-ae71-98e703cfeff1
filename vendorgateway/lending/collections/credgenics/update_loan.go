// nolint: dupl
package credgenics

import (
	"context"
	"fmt"

	// nolint:depguard
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	vgCredgenicsPb "github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics"
	credgenicsPb "github.com/epifi/gamma/api/vendors/credgenics"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/lending/collections/credgenics/utils"
)

type UpdateLoanReq struct {
	*CredgenicsHeader
	Method string
	Req    *vgCredgenicsPb.UpdateLoanRequest
	Conf   *config.Credgenics
}

func (r *UpdateLoanReq) Marshal() ([]byte, error) {
	if r.Req == nil {
		return nil, fmt.Errorf("nil request")
	}
	vgLoan := r.Req.GetLoan()
	vLoan, err := utils.ConvertLoanDetailsVGToVendor(vgLoan)
	if err != nil {
		return nil, fmt.Errorf("error converting loan details from vg to vendor: %w", err)
	}
	marshalOptions := protojson.MarshalOptions{
		EmitUnpopulated: false,
	}
	return marshalOptions.Marshal(vLoan)
}

func (r *UpdateLoanReq) URL() string {
	// {BASE_URL}/loan/{loan_id}
	return fmt.Sprintf("%s/loan/%s", r.Conf.BaseUrl, r.Req.GetLoanId())
}

func (r *UpdateLoanReq) HTTPMethod() string {
	return r.Method
}

func (r *UpdateLoanReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (r *UpdateLoanReq) GetResponse() vendorapi.Response {
	return &UpdateLoanRes{}
}

func (r *UpdateLoanReq) CanLogUnredactedEncryptedPayload() bool {
	return true
}

type UpdateLoanRes struct {
}

func (r *UpdateLoanRes) Unmarshal(b []byte) (proto.Message, error) {
	vendorRes := &credgenicsPb.UpdateLoanResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, vendorRes)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}
	logger.DebugNoCtx("credgenics update loan response", zap.Any("response", vendorRes))
	// TODO(mounish): do error handling
	return &vgCredgenicsPb.UpdateLoanResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (r *UpdateLoanRes) CanLogUnredactedEncryptedPayload() bool {
	return true
}

func (r *UpdateLoanRes) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "error in credgenics update loan", zap.Int("http_status", httpStatus), zap.String("response", string(b)))
	vendorRes := &credgenicsPb.UpdateLoanErrorResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, vendorRes)
	if err != nil {
		return &vgCredgenicsPb.UpdateLoanResponse{Status: rpc.StatusInternalWithDebugMsg(
			"error in unmarshal response")}, fmt.Errorf("failed to unmarshal response: %w", err)
	}
	if utils.IsLoanNotFoundMessage(vendorRes.GetMessage()) {
		return &vgCredgenicsPb.UpdateLoanResponse{Status: rpc.StatusRecordNotFoundWithDebugMsg("loan not found response, account might be archived")}, nil
	}
	if utils.IsNoDataToUpdateMessage(vendorRes.GetMessage()) {
		return &vgCredgenicsPb.UpdateLoanResponse{
			Status: rpc.NewStatusWithoutDebug(uint32(vgCredgenicsPb.UpdateLoanResponse_NO_DATA_TO_UPDATE), "no data to update"),
		}, nil
	}

	return &vgCredgenicsPb.UpdateLoanResponse{
		Status: vendorapi.GetRpcStatusFromHttpCodeWithDebugMsg(
			ctx,
			httpStatus,
			fmt.Sprintf("ShortMessage: %s, DebugMessage: %s", vendorRes.GetMessage(), vendorRes.GetOutput()),
		),
	}, fmt.Errorf("ErrorCode: %s, ErrorMessage: %s", vendorRes.GetMessage(), vendorRes.GetOutput())
}
