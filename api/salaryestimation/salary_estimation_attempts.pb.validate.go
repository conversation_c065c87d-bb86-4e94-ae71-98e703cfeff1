// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/salaryestimation/salary_estimation_attempts.proto

package salaryestimation

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AttemptInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AttemptInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttemptInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AttemptInfoMultiError, or
// nil if none found.
func (m *AttemptInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AttemptInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PerpetualConsentId

	// no validation rules for LatestValidOneTimeConsentId

	// no validation rules for DataSharingClientReqId

	// no validation rules for AnalysisClientReqId

	if len(errors) > 0 {
		return AttemptInfoMultiError(errors)
	}

	return nil
}

// AttemptInfoMultiError is an error wrapping multiple validation errors
// returned by AttemptInfo.ValidateAll() if the designated constraints aren't met.
type AttemptInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttemptInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttemptInfoMultiError) AllErrors() []error { return m }

// AttemptInfoValidationError is the validation error returned by
// AttemptInfo.Validate if the designated constraints aren't met.
type AttemptInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttemptInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttemptInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttemptInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttemptInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttemptInfoValidationError) ErrorName() string { return "AttemptInfoValidationError" }

// Error satisfies the builtin error interface
func (e AttemptInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttemptInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttemptInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttemptInfoValidationError{}

// Validate checks the field values on ClientParams with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ClientParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClientParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ClientParamsMultiError, or
// nil if none found.
func (m *ClientParams) ValidateAll() error {
	return m.validate(true)
}

func (m *ClientParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ClientParamsMultiError(errors)
	}

	return nil
}

// ClientParamsMultiError is an error wrapping multiple validation errors
// returned by ClientParams.ValidateAll() if the designated constraints aren't met.
type ClientParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClientParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClientParamsMultiError) AllErrors() []error { return m }

// ClientParamsValidationError is the validation error returned by
// ClientParams.Validate if the designated constraints aren't met.
type ClientParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClientParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClientParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClientParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClientParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClientParamsValidationError) ErrorName() string { return "ClientParamsValidationError" }

// Error satisfies the builtin error interface
func (e ClientParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClientParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClientParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClientParamsValidationError{}

// Validate checks the field values on SalaryEstimationAttempt with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SalaryEstimationAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SalaryEstimationAttempt with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SalaryEstimationAttemptMultiError, or nil if none found.
func (m *SalaryEstimationAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *SalaryEstimationAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if utf8.RuneCountInString(m.GetClientReqId()) < 1 {
		err := SalaryEstimationAttemptValidationError{
			field:  "ClientReqId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Source

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := SalaryEstimationAttemptValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetClientParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "ClientParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "ClientParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SalaryEstimationAttemptValidationError{
				field:  "ClientParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Step

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetAttemptInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "AttemptInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "AttemptInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAttemptInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SalaryEstimationAttemptValidationError{
				field:  "AttemptInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SalaryEstimationAttemptValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SalaryEstimationAttemptValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SalaryEstimationAttemptValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SalaryEstimationAttemptValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SalaryEstimationAttemptMultiError(errors)
	}

	return nil
}

// SalaryEstimationAttemptMultiError is an error wrapping multiple validation
// errors returned by SalaryEstimationAttempt.ValidateAll() if the designated
// constraints aren't met.
type SalaryEstimationAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SalaryEstimationAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SalaryEstimationAttemptMultiError) AllErrors() []error { return m }

// SalaryEstimationAttemptValidationError is the validation error returned by
// SalaryEstimationAttempt.Validate if the designated constraints aren't met.
type SalaryEstimationAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SalaryEstimationAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SalaryEstimationAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SalaryEstimationAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SalaryEstimationAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SalaryEstimationAttemptValidationError) ErrorName() string {
	return "SalaryEstimationAttemptValidationError"
}

// Error satisfies the builtin error interface
func (e SalaryEstimationAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSalaryEstimationAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SalaryEstimationAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SalaryEstimationAttemptValidationError{}
