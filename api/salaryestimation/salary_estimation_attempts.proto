//go:generate gen_sql -types=Source,AttemptStatus,AttemptStep
syntax = "proto3";

package salaryestimation;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/salaryestimation";
option java_package = "com.github.epifi.gamma.api.salaryestimation";

enum Source {
  SOURCE_UNSPECIFIED = 0;

  SOURCE_AA = 1;
}

// Step enum for salary estimation attempts
enum AttemptStep {
  ATTEMPT_STEP_UNSPECIFIED = 0;
  ATTEMPT_STEP_SALARY_ESTIMATION = 1;
  ATTEMPT_STEP_ACCOUNT_CONNECTION = 2;
  ATTEMPT_STEP_CONSENT_COLLECTION = 3;
  ATTEMPT_STEP_DATA_SHARING = 4;
  ATTEMPT_STEP_ANALYSIS = 5;
}

// Status enum for salary estimation attempts
enum AttemptStatus {
  ATTEMPT_STATUS_UNSPECIFIED = 0;
  ATTEMPT_STATUS_INITIATED = 1;
  ATTEMPT_STATUS_PENDING = 2;
  ATTEMPT_STATUS_IN_PROGRESS = 3;
  ATTEMPT_STATUS_SUCCESSFUL = 4;
  ATTEMPT_STATUS_FAILED = 5;
  ATTEMPT_STATUS_SKIPPED = 6;
}

// AttemptInfo contains metadata about the salary estimation attempt
message AttemptInfo {
  // Connected account IDs involved in the attempt
  repeated string connected_account_ids = 1;

  // Perpetual consent ID for the attempt
  string perpetual_consent_id = 2;

  // Latest valid one-time consent ID
  string latest_valid_one_time_consent_id = 3;

  // Data sharing client request ID
  string data_sharing_client_req_id = 4;

  // Analysis client request ID
  string analysis_client_req_id = 5;
}

// SalaryEstimationAttempt represents a salary estimation attempt record
message SalaryEstimationAttempt {
  // Unique identifier for the salary estimation attempt
  string id = 1;

  // Client request ID - unique identifier provided by the client
  string client_req_id = 2 [(validate.rules).string.min_len = 1];

  // Source of the salary estimation attempt
  Source source = 3;

  // Actor ID associated with the attempt
  string actor_id = 4 [(validate.rules).string.min_len = 1];

  // Client parameters for the attempt
  google.protobuf.Struct client_params = 5;

  // Current step of the attempt
  AttemptStep step = 6;

  // Current status of the attempt
  AttemptStatus status = 7;

  // Attempt metadata and information
  AttemptInfo attempt_info = 8;

  // Timestamp when the record was created
  google.protobuf.Timestamp created_at = 9;

  // Timestamp when the record was last updated
  google.protobuf.Timestamp updated_at = 10;

  // Timestamp when the record was deleted (soft delete)
  google.protobuf.Timestamp deleted_at = 11;
}

// Field mask for SalaryEstimationAttempt updates
enum SalaryEstimationAttemptFieldMask {
  SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UNSPECIFIED = 0;
  SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_SOURCE = 1;
  SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_CLIENT_PARAMS = 2;
  SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STEP = 3;
  SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS = 4;
  SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_ATTEMPT_INFO = 5;
  SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UPDATED_AT = 6;
  SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_DELETED_AT = 7;
}
