// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/fittt/scheduler/service.proto

package schedulerpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TriggerJobsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// trigger all jobs scheduled on or before the scheduled_till timestamp.
	ScheduledTill *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=scheduled_till,json=scheduledTill,proto3" json:"scheduled_till,omitempty"`
}

func (x *TriggerJobsRequest) Reset() {
	*x = TriggerJobsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerJobsRequest) ProtoMessage() {}

func (x *TriggerJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerJobsRequest.ProtoReflect.Descriptor instead.
func (*TriggerJobsRequest) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{0}
}

func (x *TriggerJobsRequest) GetScheduledTill() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledTill
	}
	return nil
}

type TriggerJobsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *TriggerJobsResponse) Reset() {
	*x = TriggerJobsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerJobsResponse) ProtoMessage() {}

func (x *TriggerJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerJobsResponse.ProtoReflect.Descriptor instead.
func (*TriggerJobsResponse) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{1}
}

func (x *TriggerJobsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ScheduleJobsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Schedule *Schedule `protobuf:"bytes,1,opt,name=schedule,proto3" json:"schedule,omitempty"`
}

func (x *ScheduleJobsRequest) Reset() {
	*x = ScheduleJobsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleJobsRequest) ProtoMessage() {}

func (x *ScheduleJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleJobsRequest.ProtoReflect.Descriptor instead.
func (*ScheduleJobsRequest) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{2}
}

func (x *ScheduleJobsRequest) GetSchedule() *Schedule {
	if x != nil {
		return x.Schedule
	}
	return nil
}

type ScheduleJobsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Returns first job created for the schedule.
	Job *Job `protobuf:"bytes,2,opt,name=job,proto3" json:"job,omitempty"`
}

func (x *ScheduleJobsResponse) Reset() {
	*x = ScheduleJobsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleJobsResponse) ProtoMessage() {}

func (x *ScheduleJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleJobsResponse.ProtoReflect.Descriptor instead.
func (*ScheduleJobsResponse) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{3}
}

func (x *ScheduleJobsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ScheduleJobsResponse) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

type GetJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId string `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
}

func (x *GetJobRequest) Reset() {
	*x = GetJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobRequest) ProtoMessage() {}

func (x *GetJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobRequest.ProtoReflect.Descriptor instead.
func (*GetJobRequest) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetJobRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type GetJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Job    *Job        `protobuf:"bytes,2,opt,name=job,proto3" json:"job,omitempty"`
}

func (x *GetJobResponse) Reset() {
	*x = GetJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobResponse) ProtoMessage() {}

func (x *GetJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobResponse.ProtoReflect.Descriptor instead.
func (*GetJobResponse) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetJobResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetJobResponse) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

type StopSchedulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScheduleIds []string `protobuf:"bytes,1,rep,name=schedule_ids,json=scheduleIds,proto3" json:"schedule_ids,omitempty"`
}

func (x *StopSchedulesRequest) Reset() {
	*x = StopSchedulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopSchedulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopSchedulesRequest) ProtoMessage() {}

func (x *StopSchedulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopSchedulesRequest.ProtoReflect.Descriptor instead.
func (*StopSchedulesRequest) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{6}
}

func (x *StopSchedulesRequest) GetScheduleIds() []string {
	if x != nil {
		return x.ScheduleIds
	}
	return nil
}

type StopSchedulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *StopSchedulesResponse) Reset() {
	*x = StopSchedulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopSchedulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopSchedulesResponse) ProtoMessage() {}

func (x *StopSchedulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopSchedulesResponse.ProtoReflect.Descriptor instead.
func (*StopSchedulesResponse) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{7}
}

func (x *StopSchedulesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CancelJobsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobIds []string `protobuf:"bytes,1,rep,name=job_ids,json=jobIds,proto3" json:"job_ids,omitempty"`
}

func (x *CancelJobsRequest) Reset() {
	*x = CancelJobsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelJobsRequest) ProtoMessage() {}

func (x *CancelJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelJobsRequest.ProtoReflect.Descriptor instead.
func (*CancelJobsRequest) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{8}
}

func (x *CancelJobsRequest) GetJobIds() []string {
	if x != nil {
		return x.JobIds
	}
	return nil
}

type CancelJobsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Job    *Job        `protobuf:"bytes,2,opt,name=job,proto3" json:"job,omitempty"`
}

func (x *CancelJobsResponse) Reset() {
	*x = CancelJobsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelJobsResponse) ProtoMessage() {}

func (x *CancelJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelJobsResponse.ProtoReflect.Descriptor instead.
func (*CancelJobsResponse) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{9}
}

func (x *CancelJobsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CancelJobsResponse) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

type GetJobsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page context for pagination
	PageContext *rpc.PageContextRequest `protobuf:"bytes,1,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	// optional filter to get jobs of specific schedule type. If not set, jobs of all schedule types will be returned.
	ScheduleTypes []ScheduleType `protobuf:"varint,2,rep,packed,name=schedule_types,json=scheduleTypes,proto3,enum=api.fittt.scheduler.ScheduleType" json:"schedule_types,omitempty"`
	// optional filter to get jobs with specific status. If not set, jobs of all types will be returned.
	JobStatus []JobStatus `protobuf:"varint,3,rep,packed,name=job_status,json=jobStatus,proto3,enum=api.fittt.scheduler.JobStatus" json:"job_status,omitempty"`
	// optional filter to get jobs created before or equal to the given time
	CreatedBefore *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_before,json=createdBefore,proto3" json:"created_before,omitempty"`
	// optional filter to get jobs created after or equal to the given time
	CreatedAfter *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_after,json=createdAfter,proto3" json:"created_after,omitempty"`
	// optional filter to get jobs scheduled before or equal to the given time
	ScheduledBefore *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=scheduled_before,json=scheduledBefore,proto3" json:"scheduled_before,omitempty"`
	// optional filter to get jobs scheduled after or equal to the given time
	ScheduledAfter *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=scheduled_after,json=scheduledAfter,proto3" json:"scheduled_after,omitempty"`
}

func (x *GetJobsRequest) Reset() {
	*x = GetJobsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobsRequest) ProtoMessage() {}

func (x *GetJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobsRequest.ProtoReflect.Descriptor instead.
func (*GetJobsRequest) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetJobsRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetJobsRequest) GetScheduleTypes() []ScheduleType {
	if x != nil {
		return x.ScheduleTypes
	}
	return nil
}

func (x *GetJobsRequest) GetJobStatus() []JobStatus {
	if x != nil {
		return x.JobStatus
	}
	return nil
}

func (x *GetJobsRequest) GetCreatedBefore() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedBefore
	}
	return nil
}

func (x *GetJobsRequest) GetCreatedAfter() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAfter
	}
	return nil
}

func (x *GetJobsRequest) GetScheduledBefore() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledBefore
	}
	return nil
}

func (x *GetJobsRequest) GetScheduledAfter() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAfter
	}
	return nil
}

type GetJobsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// page context response
	PageContext *rpc.PageContextResponse `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	Jobs        []*Job                   `protobuf:"bytes,3,rep,name=jobs,proto3" json:"jobs,omitempty"`
}

func (x *GetJobsResponse) Reset() {
	*x = GetJobsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobsResponse) ProtoMessage() {}

func (x *GetJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobsResponse.ProtoReflect.Descriptor instead.
func (*GetJobsResponse) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetJobsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetJobsResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetJobsResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

type GetScheduleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScheduleId string `protobuf:"bytes,1,opt,name=schedule_id,json=scheduleId,proto3" json:"schedule_id,omitempty"`
}

func (x *GetScheduleRequest) Reset() {
	*x = GetScheduleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScheduleRequest) ProtoMessage() {}

func (x *GetScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScheduleRequest.ProtoReflect.Descriptor instead.
func (*GetScheduleRequest) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetScheduleRequest) GetScheduleId() string {
	if x != nil {
		return x.ScheduleId
	}
	return ""
}

type GetScheduleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status
	Status   *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Schedule *Schedule   `protobuf:"bytes,2,opt,name=schedule,proto3" json:"schedule,omitempty"`
}

func (x *GetScheduleResponse) Reset() {
	*x = GetScheduleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScheduleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScheduleResponse) ProtoMessage() {}

func (x *GetScheduleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScheduleResponse.ProtoReflect.Descriptor instead.
func (*GetScheduleResponse) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetScheduleResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetScheduleResponse) GetSchedule() *Schedule {
	if x != nil {
		return x.Schedule
	}
	return nil
}

type GetSchedulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page context for pagination
	PageContext *rpc.PageContextRequest `protobuf:"bytes,1,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	// optional filter to get schedules of specific schedule type. If not set, schedules of all schedule types will be returned.
	ScheduleTypes []ScheduleType `protobuf:"varint,2,rep,packed,name=schedule_types,json=scheduleTypes,proto3,enum=api.fittt.scheduler.ScheduleType" json:"schedule_types,omitempty"`
	// optional filter to get schedules with specific status. If not set, schedules of all types will be returned.
	ScheduleStatus []ScheduleStatus `protobuf:"varint,3,rep,packed,name=schedule_status,json=scheduleStatus,proto3,enum=api.fittt.scheduler.ScheduleStatus" json:"schedule_status,omitempty"`
	// optional filter to get schedules created before or equal to the given time
	CreatedBefore *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_before,json=createdBefore,proto3" json:"created_before,omitempty"`
	// optional filter to get schedules created after or equal to the given time
	CreatedAfter *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_after,json=createdAfter,proto3" json:"created_after,omitempty"` // optional filter to get schedules scheduled before or equal to the given time
}

func (x *GetSchedulesRequest) Reset() {
	*x = GetSchedulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSchedulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSchedulesRequest) ProtoMessage() {}

func (x *GetSchedulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSchedulesRequest.ProtoReflect.Descriptor instead.
func (*GetSchedulesRequest) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetSchedulesRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetSchedulesRequest) GetScheduleTypes() []ScheduleType {
	if x != nil {
		return x.ScheduleTypes
	}
	return nil
}

func (x *GetSchedulesRequest) GetScheduleStatus() []ScheduleStatus {
	if x != nil {
		return x.ScheduleStatus
	}
	return nil
}

func (x *GetSchedulesRequest) GetCreatedBefore() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedBefore
	}
	return nil
}

func (x *GetSchedulesRequest) GetCreatedAfter() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAfter
	}
	return nil
}

type GetSchedulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// page context response
	PageContext *rpc.PageContextResponse `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	Schedules   []*Schedule              `protobuf:"bytes,3,rep,name=schedules,proto3" json:"schedules,omitempty"`
}

func (x *GetSchedulesResponse) Reset() {
	*x = GetSchedulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSchedulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSchedulesResponse) ProtoMessage() {}

func (x *GetSchedulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSchedulesResponse.ProtoReflect.Descriptor instead.
func (*GetSchedulesResponse) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetSchedulesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSchedulesResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetSchedulesResponse) GetSchedules() []*Schedule {
	if x != nil {
		return x.Schedules
	}
	return nil
}

type GetSportsSchedulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SportsScheduleType ScheduleType `protobuf:"varint,1,opt,name=sports_schedule_type,json=sportsScheduleType,proto3,enum=api.fittt.scheduler.ScheduleType" json:"sports_schedule_type,omitempty"`
	TournamentTag      string       `protobuf:"bytes,2,opt,name=tournament_tag,json=tournamentTag,proto3" json:"tournament_tag,omitempty"`
	// optional filter to get count of schedules created for match events after or equal to event_start
	EventStart *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=event_start,json=eventStart,proto3" json:"event_start,omitempty"`
	// optional filter to get count of schedules created for match events before or equal to event_end
	EventEnd *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=event_end,json=eventEnd,proto3" json:"event_end,omitempty"`
}

func (x *GetSportsSchedulesRequest) Reset() {
	*x = GetSportsSchedulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSportsSchedulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSportsSchedulesRequest) ProtoMessage() {}

func (x *GetSportsSchedulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSportsSchedulesRequest.ProtoReflect.Descriptor instead.
func (*GetSportsSchedulesRequest) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetSportsSchedulesRequest) GetSportsScheduleType() ScheduleType {
	if x != nil {
		return x.SportsScheduleType
	}
	return ScheduleType_SCHEDULE_TYPE_UNSPECIFIED
}

func (x *GetSportsSchedulesRequest) GetTournamentTag() string {
	if x != nil {
		return x.TournamentTag
	}
	return ""
}

func (x *GetSportsSchedulesRequest) GetEventStart() *timestamppb.Timestamp {
	if x != nil {
		return x.EventStart
	}
	return nil
}

func (x *GetSportsSchedulesRequest) GetEventEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.EventEnd
	}
	return nil
}

type GetSportsSchedulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status
	Status       *rpc.Status           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	MatchDetails []*SportsMatchDetails `protobuf:"bytes,2,rep,name=match_details,json=matchDetails,proto3" json:"match_details,omitempty"`
}

func (x *GetSportsSchedulesResponse) Reset() {
	*x = GetSportsSchedulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_fittt_scheduler_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSportsSchedulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSportsSchedulesResponse) ProtoMessage() {}

func (x *GetSportsSchedulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_fittt_scheduler_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSportsSchedulesResponse.ProtoReflect.Descriptor instead.
func (*GetSportsSchedulesResponse) Descriptor() ([]byte, []int) {
	return file_api_fittt_scheduler_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetSportsSchedulesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSportsSchedulesResponse) GetMatchDetails() []*SportsMatchDetails {
	if x != nil {
		return x.MatchDetails
	}
	return nil
}

var File_api_fittt_scheduler_service_proto protoreflect.FileDescriptor

var file_api_fittt_scheduler_service_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2f, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x13, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x69, 0x74, 0x74, 0x74, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2f, 0x6a,
	0x6f, 0x62, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x12, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x57, 0x0a, 0x12,
	0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x41, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x5f,
	0x74, 0x69, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x64, 0x54, 0x69, 0x6c, 0x6c, 0x22, 0x3a, 0x0a, 0x13, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x50, 0x0a, 0x13, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4a, 0x6f, 0x62,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x08, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72,
	0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x22, 0x67, 0x0a, 0x14, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4a,
	0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x2a, 0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x72, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x22, 0x2f, 0x0a, 0x0d,
	0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a,
	0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x61, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x03, 0x6a, 0x6f, 0x62,
	0x22, 0x45, 0x0a, 0x14, 0x53, 0x74, 0x6f, 0x70, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x0c, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x18, 0x01, 0x52, 0x0b, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x22, 0x3c, 0x0a, 0x15, 0x53, 0x74, 0x6f, 0x70, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x38, 0x0a, 0x11, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4a,
	0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x07, 0x6a, 0x6f,
	0x62, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x92, 0x01, 0x04, 0x08, 0x01, 0x18, 0x01, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x73, 0x22,
	0x65, 0x0a, 0x12, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x03, 0x6a, 0x6f,
	0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69,
	0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x4a, 0x6f,
	0x62, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x22, 0xe5, 0x03, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4a, 0x6f,
	0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x3d, 0x0a, 0x0a, 0x6a, 0x6f, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x4a, 0x6f, 0x62, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x09, 0x6a, 0x6f, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41,
	0x0a, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x65, 0x66, 0x6f, 0x72,
	0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x66, 0x74,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x66, 0x74,
	0x65, 0x72, 0x12, 0x45, 0x0a, 0x10, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x5f,
	0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x64, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x12, 0x43, 0x0a, 0x0f, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x41, 0x66, 0x74, 0x65, 0x72, 0x22, 0xa1,
	0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x2c, 0x0a, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x04, 0x6a, 0x6f,
	0x62, 0x73, 0x22, 0x3e, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x49, 0x64, 0x22, 0x75, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39,
	0x0a, 0x08, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52,
	0x08, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x22, 0xed, 0x02, 0x0a, 0x13, 0x47, 0x65,
	0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x48, 0x0a,
	0x0e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74,
	0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x4c, 0x0a, 0x0f, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x66, 0x74, 0x65, 0x72, 0x22, 0xb5, 0x01, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x3b, 0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69,
	0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x09, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x73, 0x22, 0xa2, 0x02, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x5f, 0x0a, 0x14, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x18, 0x01, 0x18, 0x02, 0x52, 0x12, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2e, 0x0a, 0x0e, 0x74, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0d, 0x74, 0x6f, 0x75, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67,
	0x12, 0x3b, 0x0a, 0x0b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x37, 0x0a,
	0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x22, 0x8f, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x53, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4c, 0x0a, 0x0d, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x32, 0x99, 0x07, 0x0a, 0x10, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x65, 0x0a,
	0x0c, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x28, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4a, 0x6f, 0x62, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69,
	0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x62, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x77, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x2e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x68, 0x0a, 0x0d, 0x53, 0x74, 0x6f, 0x70, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x73, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x0a, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4a, 0x6f,
	0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x0c,
	0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x28, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74,
	0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x12, 0x22, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x4a,
	0x6f, 0x62, 0x73, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66,
	0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x62, 0x0a, 0x0b, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4a, 0x6f, 0x62, 0x73, 0x12,
	0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4a, 0x6f, 0x62,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66,
	0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x54,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x42, 0x64, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x72, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x69, 0x74, 0x74, 0x74, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x3b, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_fittt_scheduler_service_proto_rawDescOnce sync.Once
	file_api_fittt_scheduler_service_proto_rawDescData = file_api_fittt_scheduler_service_proto_rawDesc
)

func file_api_fittt_scheduler_service_proto_rawDescGZIP() []byte {
	file_api_fittt_scheduler_service_proto_rawDescOnce.Do(func() {
		file_api_fittt_scheduler_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_fittt_scheduler_service_proto_rawDescData)
	})
	return file_api_fittt_scheduler_service_proto_rawDescData
}

var file_api_fittt_scheduler_service_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_api_fittt_scheduler_service_proto_goTypes = []interface{}{
	(*TriggerJobsRequest)(nil),         // 0: api.fittt.scheduler.TriggerJobsRequest
	(*TriggerJobsResponse)(nil),        // 1: api.fittt.scheduler.TriggerJobsResponse
	(*ScheduleJobsRequest)(nil),        // 2: api.fittt.scheduler.ScheduleJobsRequest
	(*ScheduleJobsResponse)(nil),       // 3: api.fittt.scheduler.ScheduleJobsResponse
	(*GetJobRequest)(nil),              // 4: api.fittt.scheduler.GetJobRequest
	(*GetJobResponse)(nil),             // 5: api.fittt.scheduler.GetJobResponse
	(*StopSchedulesRequest)(nil),       // 6: api.fittt.scheduler.StopSchedulesRequest
	(*StopSchedulesResponse)(nil),      // 7: api.fittt.scheduler.StopSchedulesResponse
	(*CancelJobsRequest)(nil),          // 8: api.fittt.scheduler.CancelJobsRequest
	(*CancelJobsResponse)(nil),         // 9: api.fittt.scheduler.CancelJobsResponse
	(*GetJobsRequest)(nil),             // 10: api.fittt.scheduler.GetJobsRequest
	(*GetJobsResponse)(nil),            // 11: api.fittt.scheduler.GetJobsResponse
	(*GetScheduleRequest)(nil),         // 12: api.fittt.scheduler.GetScheduleRequest
	(*GetScheduleResponse)(nil),        // 13: api.fittt.scheduler.GetScheduleResponse
	(*GetSchedulesRequest)(nil),        // 14: api.fittt.scheduler.GetSchedulesRequest
	(*GetSchedulesResponse)(nil),       // 15: api.fittt.scheduler.GetSchedulesResponse
	(*GetSportsSchedulesRequest)(nil),  // 16: api.fittt.scheduler.GetSportsSchedulesRequest
	(*GetSportsSchedulesResponse)(nil), // 17: api.fittt.scheduler.GetSportsSchedulesResponse
	(*timestamppb.Timestamp)(nil),      // 18: google.protobuf.Timestamp
	(*rpc.Status)(nil),                 // 19: rpc.Status
	(*Schedule)(nil),                   // 20: api.fittt.scheduler.Schedule
	(*Job)(nil),                        // 21: api.fittt.scheduler.Job
	(*rpc.PageContextRequest)(nil),     // 22: rpc.PageContextRequest
	(ScheduleType)(0),                  // 23: api.fittt.scheduler.ScheduleType
	(JobStatus)(0),                     // 24: api.fittt.scheduler.JobStatus
	(*rpc.PageContextResponse)(nil),    // 25: rpc.PageContextResponse
	(ScheduleStatus)(0),                // 26: api.fittt.scheduler.ScheduleStatus
	(*SportsMatchDetails)(nil),         // 27: api.fittt.scheduler.SportsMatchDetails
}
var file_api_fittt_scheduler_service_proto_depIdxs = []int32{
	18, // 0: api.fittt.scheduler.TriggerJobsRequest.scheduled_till:type_name -> google.protobuf.Timestamp
	19, // 1: api.fittt.scheduler.TriggerJobsResponse.status:type_name -> rpc.Status
	20, // 2: api.fittt.scheduler.ScheduleJobsRequest.schedule:type_name -> api.fittt.scheduler.Schedule
	19, // 3: api.fittt.scheduler.ScheduleJobsResponse.status:type_name -> rpc.Status
	21, // 4: api.fittt.scheduler.ScheduleJobsResponse.job:type_name -> api.fittt.scheduler.Job
	19, // 5: api.fittt.scheduler.GetJobResponse.status:type_name -> rpc.Status
	21, // 6: api.fittt.scheduler.GetJobResponse.job:type_name -> api.fittt.scheduler.Job
	19, // 7: api.fittt.scheduler.StopSchedulesResponse.status:type_name -> rpc.Status
	19, // 8: api.fittt.scheduler.CancelJobsResponse.status:type_name -> rpc.Status
	21, // 9: api.fittt.scheduler.CancelJobsResponse.job:type_name -> api.fittt.scheduler.Job
	22, // 10: api.fittt.scheduler.GetJobsRequest.page_context:type_name -> rpc.PageContextRequest
	23, // 11: api.fittt.scheduler.GetJobsRequest.schedule_types:type_name -> api.fittt.scheduler.ScheduleType
	24, // 12: api.fittt.scheduler.GetJobsRequest.job_status:type_name -> api.fittt.scheduler.JobStatus
	18, // 13: api.fittt.scheduler.GetJobsRequest.created_before:type_name -> google.protobuf.Timestamp
	18, // 14: api.fittt.scheduler.GetJobsRequest.created_after:type_name -> google.protobuf.Timestamp
	18, // 15: api.fittt.scheduler.GetJobsRequest.scheduled_before:type_name -> google.protobuf.Timestamp
	18, // 16: api.fittt.scheduler.GetJobsRequest.scheduled_after:type_name -> google.protobuf.Timestamp
	19, // 17: api.fittt.scheduler.GetJobsResponse.status:type_name -> rpc.Status
	25, // 18: api.fittt.scheduler.GetJobsResponse.page_context:type_name -> rpc.PageContextResponse
	21, // 19: api.fittt.scheduler.GetJobsResponse.jobs:type_name -> api.fittt.scheduler.Job
	19, // 20: api.fittt.scheduler.GetScheduleResponse.status:type_name -> rpc.Status
	20, // 21: api.fittt.scheduler.GetScheduleResponse.schedule:type_name -> api.fittt.scheduler.Schedule
	22, // 22: api.fittt.scheduler.GetSchedulesRequest.page_context:type_name -> rpc.PageContextRequest
	23, // 23: api.fittt.scheduler.GetSchedulesRequest.schedule_types:type_name -> api.fittt.scheduler.ScheduleType
	26, // 24: api.fittt.scheduler.GetSchedulesRequest.schedule_status:type_name -> api.fittt.scheduler.ScheduleStatus
	18, // 25: api.fittt.scheduler.GetSchedulesRequest.created_before:type_name -> google.protobuf.Timestamp
	18, // 26: api.fittt.scheduler.GetSchedulesRequest.created_after:type_name -> google.protobuf.Timestamp
	19, // 27: api.fittt.scheduler.GetSchedulesResponse.status:type_name -> rpc.Status
	25, // 28: api.fittt.scheduler.GetSchedulesResponse.page_context:type_name -> rpc.PageContextResponse
	20, // 29: api.fittt.scheduler.GetSchedulesResponse.schedules:type_name -> api.fittt.scheduler.Schedule
	23, // 30: api.fittt.scheduler.GetSportsSchedulesRequest.sports_schedule_type:type_name -> api.fittt.scheduler.ScheduleType
	18, // 31: api.fittt.scheduler.GetSportsSchedulesRequest.event_start:type_name -> google.protobuf.Timestamp
	18, // 32: api.fittt.scheduler.GetSportsSchedulesRequest.event_end:type_name -> google.protobuf.Timestamp
	19, // 33: api.fittt.scheduler.GetSportsSchedulesResponse.status:type_name -> rpc.Status
	27, // 34: api.fittt.scheduler.GetSportsSchedulesResponse.match_details:type_name -> api.fittt.scheduler.SportsMatchDetails
	2,  // 35: api.fittt.scheduler.SchedulerService.ScheduleJobs:input_type -> api.fittt.scheduler.ScheduleJobsRequest
	12, // 36: api.fittt.scheduler.SchedulerService.GetSchedule:input_type -> api.fittt.scheduler.GetScheduleRequest
	16, // 37: api.fittt.scheduler.SchedulerService.GetSportsSchedules:input_type -> api.fittt.scheduler.GetSportsSchedulesRequest
	6,  // 38: api.fittt.scheduler.SchedulerService.StopSchedules:input_type -> api.fittt.scheduler.StopSchedulesRequest
	8,  // 39: api.fittt.scheduler.SchedulerService.CancelJobs:input_type -> api.fittt.scheduler.CancelJobsRequest
	14, // 40: api.fittt.scheduler.SchedulerService.GetSchedules:input_type -> api.fittt.scheduler.GetSchedulesRequest
	4,  // 41: api.fittt.scheduler.SchedulerService.GetJob:input_type -> api.fittt.scheduler.GetJobRequest
	10, // 42: api.fittt.scheduler.SchedulerService.GetJobs:input_type -> api.fittt.scheduler.GetJobsRequest
	0,  // 43: api.fittt.scheduler.SchedulerService.TriggerJobs:input_type -> api.fittt.scheduler.TriggerJobsRequest
	3,  // 44: api.fittt.scheduler.SchedulerService.ScheduleJobs:output_type -> api.fittt.scheduler.ScheduleJobsResponse
	13, // 45: api.fittt.scheduler.SchedulerService.GetSchedule:output_type -> api.fittt.scheduler.GetScheduleResponse
	17, // 46: api.fittt.scheduler.SchedulerService.GetSportsSchedules:output_type -> api.fittt.scheduler.GetSportsSchedulesResponse
	7,  // 47: api.fittt.scheduler.SchedulerService.StopSchedules:output_type -> api.fittt.scheduler.StopSchedulesResponse
	9,  // 48: api.fittt.scheduler.SchedulerService.CancelJobs:output_type -> api.fittt.scheduler.CancelJobsResponse
	15, // 49: api.fittt.scheduler.SchedulerService.GetSchedules:output_type -> api.fittt.scheduler.GetSchedulesResponse
	5,  // 50: api.fittt.scheduler.SchedulerService.GetJob:output_type -> api.fittt.scheduler.GetJobResponse
	11, // 51: api.fittt.scheduler.SchedulerService.GetJobs:output_type -> api.fittt.scheduler.GetJobsResponse
	1,  // 52: api.fittt.scheduler.SchedulerService.TriggerJobs:output_type -> api.fittt.scheduler.TriggerJobsResponse
	44, // [44:53] is the sub-list for method output_type
	35, // [35:44] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_api_fittt_scheduler_service_proto_init() }
func file_api_fittt_scheduler_service_proto_init() {
	if File_api_fittt_scheduler_service_proto != nil {
		return
	}
	file_api_fittt_scheduler_jobs_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_fittt_scheduler_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerJobsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerJobsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScheduleJobsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScheduleJobsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopSchedulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopSchedulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelJobsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelJobsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScheduleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScheduleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSchedulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSchedulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSportsSchedulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_fittt_scheduler_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSportsSchedulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_fittt_scheduler_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_fittt_scheduler_service_proto_goTypes,
		DependencyIndexes: file_api_fittt_scheduler_service_proto_depIdxs,
		MessageInfos:      file_api_fittt_scheduler_service_proto_msgTypes,
	}.Build()
	File_api_fittt_scheduler_service_proto = out.File
	file_api_fittt_scheduler_service_proto_rawDesc = nil
	file_api_fittt_scheduler_service_proto_goTypes = nil
	file_api_fittt_scheduler_service_proto_depIdxs = nil
}
