update onboarding_step_details
set staled_at=NOW()
where id = (
	-- fetch the latest step row
	select id
	from onboarding_step_details
	where onboarding_details_id = (
		select id
		from onboarding_details
		where actor_id = 'AC2109179s2egkOeQlC2AkaHVq0a/g=='
		  and onboarding_type = 'ONBOARDING_TYPE_WEALTH'
	)
	  AND step = 'ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET'
	order by created_at desc
	limit 1
	);
update onboarding_details
set status='ONBOARDING_STATUS_IN_PROGRESS', current_step='ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET',
    kra_data=jsonb_build_object('statusData', kra_data->'statusData')
where actor_id = 'AC2109179s2egkOeQlC2AkaHVq0a/g==' and onboarding_type = 'ONBOARDING_TYPE_WEALTH';
