CREATE TABLE IF NOT EXISTS salary_estimation_attempts (
	id                  UUID        DEFAULT gen_random_uuid() PRIMARY KEY,
	client_req_id       VARCHAR      NOT NULL,
	source              VARCHAR      NOT NULL DEFAULT 'SOURCE_UNSPECIFIED',
	actor_id            VARCHAR      NOT NULL,
	client_params       JSONB       NULL,
	step                VARCHAR      NOT NULL DEFAULT 'ATTEMPT_STEP_UNSPECIFIED',
	status              VARCHAR      NOT NULL DEFAULT 'ATTEMPT_STATUS_UNSPECIFIED',
	attempt_info        JSONB       NULL,
	created_at          TIMESTAMPTZ NOT NULL DEFAULT now(),
	updated_at          TIMESTAMPTZ NOT NULL DEFAULT now(),
	deleted_at          TIMESTAMPTZ NULL,

	CONSTRAINT salary_estimation_attempts_unique_client_req_id_idx UNIQUE (client_req_id)
);
